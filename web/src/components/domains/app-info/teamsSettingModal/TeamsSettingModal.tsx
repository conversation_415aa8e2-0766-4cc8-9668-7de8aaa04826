import * as React from 'react';
import {
  <PERSON><PERSON>, <PERSON><PERSON>, Input, Loader, Tooltip,
} from '@fluentui/react-northstar';
import {
  AddIcon, AcceptIcon, CloseIcon, InfoIcon,
} from '@fluentui/react-icons-northstar';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import { mergedClassName } from '../../../../utilities/commonFunction';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import { isSP } from '../../../../utilities/mediaQuery';
import useUserChatsAndChannelsAccessor, { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';
import useComponentInitUtility from '../../../../hooks/utilities/useComponentInitUtility';
import TeamsSettingTabs, { TeamsSettingTabType, TeamsSettingTabTypeValue }
  from './TeamsSettingTabs';
import SelectedItemsList, { ISelectedItem } from './SelectedItemsList';
import { UseTeamsChatsApiReturnType } from '../../../../hooks/accessors/useTeamsChatsApiAccessor';
import MessageToaster, { ToasterMessage } from '../../../commons/molecules/message-toaster/MessageToaster';
import useMessageToasterBehavior from '../../../../hooks/behaviors/useMessageToasterBehavior';
import useTeamsSettingData from '../../../../hooks/features/useTeamsSettingData';
import useIndexedDbAccessor from '../../../../hooks/accessors/useIndexedDbAccessor';

// CSS
import './TeamsSettingModal.scss';

// チャットアイテムの型定義
export interface IChatItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
}

export interface ITeamsModalProps {
  className?: string;
  // Teams設定の文言。環境変数から取得
  searchRestriction?:string,
  open?: boolean;
  onClose: () => void;
  useTeamsChatsApiAccessorReturn: UseTeamsChatsApiReturnType,
  eventReporter: EventReporter;
}

/**
 * TeamsSettingModal
 * @param props
 */
const TeamsSettingModal: React.FC<ITeamsModalProps> = (props) => {
  const {
    className,
    searchRestriction,
    open,
    onClose,
    useTeamsChatsApiAccessorReturn,
    eventReporter,
  } = props;

  const {
    // 新規登録、更新
    postTeamsChatsApi,
    // 同期
    getTeamsChatsApi,
    // 削除
    deleteTeamsChatsApi,
  } = useTeamsChatsApiAccessorReturn;

  /**
   * ラベル
   */
  const TeamsSettingLabel = {
    TITLE: 'Teams設定',
  };

  // コンポーネント初期化ユーティリティ
  const [, , callbacks] = useComponentInitUtility({
    componentName: 'TeamsSettingModal',
  });
  const tokenProvider = React.useMemo(() => {
    if (!callbacks?.get) return undefined;
    const graphTokenProvider = callbacks.get('graph');
    return graphTokenProvider ? () => graphTokenProvider() : undefined;
  }, [callbacks]);

  // APIアクセサーを初期化
  const {
    // ユーザーが参加しているチャット/チャネルをページング処理で取得
    fetchUserChatsAndChannelsPaginated,
    // 画面に総件数を表示する関数
    fetchUserChatsTotalCount,
    isLoading, error,
  } = useUserChatsAndChannelsAccessor(tokenProvider);

  // IndexedDB features
  const [openDB] = useIndexedDbAccessor();

  // TeamsSettingDataフックを使用
  const {
    allChatItems,
    savedItems,
    savedSelectedItems,
    isLoadingSavedItems,
    hasMoreChats,
    isLoadingMore,
    totalChatCount,
    totalChannelCount,
    isLoadingTotalCount,
    saveSelectedItems,
    setSavedItems,
    loadMoreChats,
  } = useTeamsSettingData({
    fetchUserChatsAndChannelsPaginated,
    fetchUserChatsTotalCount,
    getTeamsChatsApi,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    isModalOpen: open || false,
    openDB,
    eventReporter,
  });

  // 検索状態管理
  const [searchQuery, setSearchQuery] = React.useState('');
  // 検索後アイテム
  const [filteredChatItems, setFilteredChatItems] = React.useState<IUserChatItem[]>([]);
  // 選択されたアイテム
  const [selectedItems, setSelectedItems] = React.useState<Set<ISelectedItem>>(new Set());
  // タブ状態管理
  const [activeTab, setActiveTab] = React.useState<TeamsSettingTabTypeValue>(
    TeamsSettingTabType.CHAT,
  );
  // 保存状態管理
  const [isSaving, setIsSaving] = React.useState(false);
  // 保存完了状態管理
  const [isSaveCompleted, setIsSaveCompleted] = React.useState(false);
  // トースター機能
  const [isToasterShown, toasterMessage, extendPopupTimer] = useMessageToasterBehavior(3000);
  // 選択上限数
  const MAX_SELECTION_COUNT = 10;
  // 統合されたローディング状態
  const isLoadingData = isLoading || isLoadingSavedItems;
  // デバイス判定（モバイルでは「さらに読み込む」ボタンを非表示）
  const isMobile = React.useMemo(() => isSP(), []);

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('modal', className);
    const isOpen = open ? 'is-open' : '';
    const hasSelectedItems = selectedItems.size > 0 ? 'has-selected-items' : '';
    return mergedClassName(mergedClassName(isOpen, step1), hasSelectedItems);
  }, [className, open, selectedItems.size]);

  // 保存済みアイテムを選択状態に反映するEffect
  React.useEffect(() => {
    // savedSelectedItemsをそのまま使用
    setSelectedItems(new Set(savedSelectedItems));
  }, [savedSelectedItems]);

  // 検索フィルタリングのEffect（タブとテキスト検索の両方に対応）
  React.useEffect(() => {
    let filtered = allChatItems;

    // タブによるフィルタリング
    filtered = filtered.filter((item) => item.type === activeTab);

    // テキスト検索によるフィルタリング
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (item) => item.id.toLowerCase().includes(searchQuery.toLowerCase())
        || item.name.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    setFilteredChatItems(filtered);
  }, [searchQuery, allChatItems, activeTab]);

  const handleClose = React.useCallback(() => {
    // 状態をリセット
    setSearchQuery('');
    setSelectedItems(new Set());
    setSavedItems(new Set());
    setActiveTab(TeamsSettingTabType.CHAT);
    setIsSaving(false);
    setIsSaveCompleted(false);

    if (onClose) onClose();
  }, [onClose, setSavedItems]);

  // 検索クエリ入力の変更ハンドラー
  const handleSearchQueryChange = React.useCallback(
    (_e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => {
      setSearchQuery(data?.value ?? '');
    },
    [],
  );

  // タブ変更ハンドラー
  const handleTabChange = React.useCallback((tab: TeamsSettingTabTypeValue) => {
    setActiveTab(tab);
    // タブ切り替え時に検索クエリをクリア
    setSearchQuery('');
  }, []);

  // チャットとチャネルの件数を計算
  const chatCount = React.useMemo(() => allChatItems.filter((item) => item.type
  === TeamsSettingTabType.CHAT).length, [allChatItems]);

  const channelCount = React.useMemo(() => allChatItems.filter((item) => item.type
  === TeamsSettingTabType.CHANNEL).length, [allChatItems]);

  // プレースホルダーテキストをタブに応じて変更
  const searchPlaceholder = React.useMemo(() => (activeTab === TeamsSettingTabType.CHAT
    ? 'チャット名で検索' : 'チャネル名で検索'), [activeTab]);

  // 保存ボタンのコンテンツ
  const saveButtonContent = React.useMemo(() => {
    if (isSaving) {
      return (
        <>
          <Loader size="smallest" inline />
          {' '}
          保存中...
        </>
      );
    }
    if (isSaveCompleted) {
      return '保存完了';
    }
    return '保存';
  }, [isSaving, isSaveCompleted]);

  // さらに読み込むボタンのハンドラー
  const handleLoadMoreChats = React.useCallback(() => {
    loadMoreChats();
  }, [loadMoreChats]);

  // アイテム選択切り替えハンドラー
  const handleItemToggle = React.useCallback((chatItem: IUserChatItem) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      // 既存のアイテムを検索（IDで比較）
      const existingItem = Array.from(newSet).find((item) => item.id === chatItem.id);

      if (existingItem) {
        // 選択解除の場合
        newSet.delete(existingItem);
      } else {
        // 選択追加の場合：上限チェック
        if (newSet.size >= MAX_SELECTION_COUNT) {
          // 上限に達している場合はトースターメッセージを表示
          extendPopupTimer(ToasterMessage.MAX_TEAMS_SELECTION);
          return prev; // 状態を変更しない
        }
        // IUserChatItemをISelectedItemに変換して追加
        const selectedItem: ISelectedItem = {
          id: chatItem.id,
          name: chatItem.name,
          type: chatItem.type,
          chatType: chatItem.chatType,
          teamId: chatItem.teamId,
          countId: newSet.size + 1, // 選択順序
        };
        newSet.add(selectedItem);
      }
      return newSet;
    });
  }, [extendPopupTimer]);

  // 選択されたアイテムを削除するハンドラー
  const handleRemoveSelectedItem = React.useCallback((item: ISelectedItem) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      newSet.delete(item);
      return newSet;
    });
  }, []);

  // キーボードイベントハンドラー
  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, chatItem: IUserChatItem) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleItemToggle(chatItem);
    }
  }, [handleItemToggle]);

  // 保存ハンドラー
  const handleSave = React.useCallback(async () => {
    if (!saveSelectedItems) {
      throw new Error('保存機能が利用できません');
    }
    // ローディング状態を開始
    setIsSaving(true);
    try {
      // selectedItemsを直接渡す
      await saveSelectedItems(selectedItems);
      // 保存完了状態を表示
      setIsSaving(false);
      setIsSaveCompleted(true);
      // 0.8秒後にモーダルを閉じる
      setTimeout(() => {
        handleClose();
        // 閉じる時間を管理
      }, 800);
    } finally {
      // ローディング状態を終了（保存完了時は上で既に設定済み）
      if (!isSaveCompleted) {
        setIsSaving(false);
      }
    }
  }, [
    saveSelectedItems,
    selectedItems,
    handleClose,
    isSaveCompleted]);

  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="modal-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="modal-close-pc">
        <Button
          className="modal-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div className="modal-scroll-wrapper">
        <div className="modal-scroll-inner">
          <div className="modal-header">
            <Header content={TeamsSettingLabel.TITLE} as="h3" className="modal-title" />
          </div>
          <div className="modal-content">
            <div className="modal-description">
              <p>検索対象を選択できます。最大10件まで選択可能です。</p>
              <Tooltip
                content={searchRestriction}
                trigger={(
                  <Button
                    text
                    iconOnly
                    icon={<InfoIcon />}
                    className="teams-setting-info-button"
                    aria-label="データ取得に関する情報"
                  />
                )}
              />
            </div>
            {/* タブ切り替え */}
            <TeamsSettingTabs
              activeTab={activeTab}
              onTabChange={handleTabChange}
              disabled={isLoadingData}
              chatCount={chatCount}
              channelCount={channelCount}
            />
            {/* 検索フィールド */}
            <div className="modal-chat-input">
              <Input
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={handleSearchQueryChange}
                fluid
              />
            </div>
            {/* 選択されたアイテム一覧 */}
            {selectedItems.size > 0 && (
            <div className="selected-items-count">
              <span className="selected-items-count-text">
                選択中アイテム (
                {selectedItems.size}
                /
                {MAX_SELECTION_COUNT}
                )
              </span>
            </div>
            )}
            <SelectedItemsList
              selectedItems={selectedItems}
              onRemoveItem={handleRemoveSelectedItem}
            />
            {/* チャットアイテム一覧 */}
            <div className="modal-chat-items">
              {isLoadingData && (
              <div className="modal-loading">
                <p>チャットを読み込み中...</p>
              </div>
              )}
              {error && (
              <div className="modal-error">
                <p>
                  エラーが発生しました:
                  {error}
                </p>
              </div>
              )}
              {!isLoadingData && !error && filteredChatItems.length === 0 && (
              <div className="modal-no-results">
                <p>該当するチャットまたはチャネルが見つかりませんでした。</p>
              </div>
              )}
              {!isLoadingData && !error && filteredChatItems.map((item) => {
                // IDで選択状態をチェック
                const isSelected = Array.from(selectedItems).some(
                  (selectedItem) => selectedItem.id === item.id,
                );
                const itemClassName = `modal-chat-item${isSelected ? ' selected' : ''}`;
                return (
                  <div
                    key={item.id}
                    className={itemClassName}
                    onClick={() => handleItemToggle(item)}
                    onKeyDown={(event) => handleKeyDown(event, item)}
                    role="button"
                    tabIndex={0}
                    aria-pressed={isSelected}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="modal-chat-item-content">
                      <span className="modal-chat-item-name">{item.name}</span>
                    </div>
                    {isSelected ? (
                      <AcceptIcon
                        style={{
                          color: 'var(--color-guide-brand-icon)',
                          fontSize: '20px',
                          transform: 'scale(1.1)',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    ) : (
                      <AddIcon
                        style={{
                          color: 'var(--color-guide-foreground-2)',
                          fontSize: '20px',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    )}
                  </div>
                );
              })}
            </div>
            {/* さらに読み込むボタン */}
            {!isLoadingData && !error && hasMoreChats && (
            <div className="load-more-button">
              <Button
                content={isLoadingMore ? '読み込み中...' : 'さらに読み込む'}
                disabled={isLoadingMore}
                onClick={handleLoadMoreChats}
                size="small"
                loading={isLoadingMore}
              />
              <div className="total-items-info">
                <span>
                  {allChatItems.length}
                  件表示中
                  {(() => {
                    const currentTotalCount = activeTab === TeamsSettingTabType.CHAT
                      ? totalChatCount
                      : totalChannelCount;

                    if (currentTotalCount !== undefined) {
                      return (
                        <>
                          {' / 全'}
                          {currentTotalCount}
                          件
                        </>
                      );
                    }

                    if (isLoadingTotalCount && currentTotalCount === undefined) {
                      return <> / 総件数取得中...</>;
                    }

                    return null;
                  })()}
                </span>
              </div>
            </div>
            )}
            <div className="modal-save-button">
              <Button
                primary={!isSaveCompleted}
                content={saveButtonContent}
                size={isMobile ? 'small' : undefined}
                fluid={!isMobile}
                disabled={
                  !postTeamsChatsApi
                  || !deleteTeamsChatsApi
                  || isSaving
                  || isSaveCompleted
                  || (selectedItems.size === 0 && savedItems.size === 0)
                  || (selectedItems.size === savedItems.size
                      && Array.from(selectedItems).every((item) => savedItems.has(item.id)))
                }
                onClick={handleSave}
                styles={isSaveCompleted ? {
                  root: {
                    backgroundColor: '#107c10',
                    borderColor: '#107c10',
                    color: 'white',
                  },
                } : undefined}
              />
            </div>
          </div>
        </div>
      </div>
      {/* トースターメッセージ */}
      <MessageToaster
        isActive={isToasterShown}
        messageType={toasterMessage}
      />
    </div>
  );
};

TeamsSettingModal.defaultProps = {
  className: '',
  searchRestriction: '',
  open: false,
};

export default TeamsSettingModal;
